(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-phone/index"],{"0c5a":function(n,t,e){"use strict";e.r(t);var u=e("69f5"),o=e.n(u);for(var c in u)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(c);t["default"]=o.a},"69f5":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var u=e("7723"),o={props:{phoneData:{type:String,default:""}},methods:{call:function(){(0,u.call)(this.phoneData)},closePopup:function(){this.$emit("closePopup")}}};t.default=o},c46c:function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return c})),e.d(t,"a",(function(){return u}));var u={uniPopup:function(){return e.e("uni_modules/uni-popup/components/uni-popup/uni-popup").then(e.bind(null,"8300a"))}},o=function(){var n=this.$createElement;this._self._c},c=[]},de6e:function(n,t,e){"use strict";e.r(t);var u=e("c46c"),o=e("0c5a");for(var c in o)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(c);e("3f1d");var i=e("828b"),a=Object(i["a"])(o["default"],u["b"],u["c"],!1,null,"26c6f860",null,!1,u["a"],void 0);t["default"]=a.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-phone/index-create-component',
    {
        'components/uni-phone/index-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("de6e"))
        })
    },
    [['components/uni-phone/index-create-component']]
]);
