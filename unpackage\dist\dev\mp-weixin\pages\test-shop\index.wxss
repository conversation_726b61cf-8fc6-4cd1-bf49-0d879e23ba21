
.test-container.data-v-3be3ef2b {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}
.header.data-v-3be3ef2b {
  text-align: center;
  margin-bottom: 40rpx;
}
.title.data-v-3be3ef2b {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.section-title.data-v-3be3ef2b {
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}
.current-config.data-v-3be3ef2b {
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.config-item.data-v-3be3ef2b {
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
}
.label.data-v-3be3ef2b {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}
.value.data-v-3be3ef2b {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}
.test-buttons.data-v-3be3ef2b {
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}
.button-row.data-v-3be3ef2b {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.test-btn.data-v-3be3ef2b {
  flex: 1;
  margin: 0 10rpx;
  padding: 20rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}
.test-btn.data-v-3be3ef2b:active {
  background-color: #0056cc;
}
.navigation.data-v-3be3ef2b {
  text-align: center;
}
.nav-btn.data-v-3be3ef2b {
  width: 300rpx;
  padding: 25rpx;
  background-color: #ff6b35;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 30rpx;
}
.nav-btn.data-v-3be3ef2b:active {
  background-color: #e55a2b;
}

