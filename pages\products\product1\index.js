import midManager from "../../../utils/midManager.js"

export default {
  data() {
    return {
      productInfo: {
        id: 1,
        name: '精选美味商品1',
        price: 29.90,
        image: '/static/logo_ruiji.png',
        description: '这是一款精心制作的美味商品，采用优质食材，口感丰富，营养均衡。'
      }
    }
  },

  onLoad(options) {
    // 页面加载时的逻辑
    console.log('商品1页面加载')

    // 如果URL中有mid参数，保存它
    if (options && options.mid) {
      midManager.setMid(options.mid)
    }
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack({
        delta: 1
      })
    },

    // 跳转到下单页面
    goToOrder() {
      // 使用midManager自动添加保存的mid参数
      midManager.navigateTo('/pages/index/index?mid=2')
    }
  }
}
