<view class="test-container data-v-3be3ef2b"><view class="header data-v-3be3ef2b"><text class="title data-v-3be3ef2b">店铺配置测试页面</text></view><view class="current-config data-v-3be3ef2b"><text class="section-title data-v-3be3ef2b">{{"当前配置 (mid: "+(currentMid||'无')+")"}}</text><view class="config-item data-v-3be3ef2b"><text class="label data-v-3be3ef2b">店铺名称:</text><text class="value data-v-3be3ef2b">{{currentConfig.shopName}}</text></view><view class="config-item data-v-3be3ef2b"><text class="label data-v-3be3ef2b">店铺地址:</text><text class="value data-v-3be3ef2b">{{currentConfig.shopAddress}}</text></view><view class="config-item data-v-3be3ef2b"><text class="label data-v-3be3ef2b">联系电话:</text><text class="value data-v-3be3ef2b">{{currentConfig.phone}}</text></view></view><view class="test-buttons data-v-3be3ef2b"><text class="section-title data-v-3be3ef2b">测试不同 mid</text><view class="button-row data-v-3be3ef2b"><button data-event-opts="{{[['tap',[['testMid',['']]]]]}}" class="test-btn data-v-3be3ef2b" bindtap="__e">默认 (无mid)</button><button data-event-opts="{{[['tap',[['testMid',['1']]]]]}}" class="test-btn data-v-3be3ef2b" bindtap="__e">mid=1</button></view><view class="button-row data-v-3be3ef2b"><button data-event-opts="{{[['tap',[['testMid',['2']]]]]}}" class="test-btn data-v-3be3ef2b" bindtap="__e">mid=2</button><button data-event-opts="{{[['tap',[['testMid',['3']]]]]}}" class="test-btn data-v-3be3ef2b" bindtap="__e">mid=3</button></view></view><view class="navigation data-v-3be3ef2b"><button data-event-opts="{{[['tap',[['goToIndex',['$event']]]]]}}" class="nav-btn data-v-3be3ef2b" bindtap="__e">返回首页</button><button data-event-opts="{{[['tap',[['showHistory',['$event']]]]]}}" class="history-btn data-v-3be3ef2b" bindtap="__e">查看历史</button></view></view>