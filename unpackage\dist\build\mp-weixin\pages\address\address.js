(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/address/address"],{"064d":function(t,e,n){"use strict";var r=n("8d7b"),i=n.n(r);i.a},1262:function(t,e,n){"use strict";(function(t,e){var r=n("47a9");n("b759");r(n("3240"));var i=r(n("26e3"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(i.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"26e3":function(t,e,n){"use strict";n.r(e);var r=n("9fc2"),i=n("5d43");for(var s in i)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(s);n("064d");var a=n("828b"),o=Object(a["a"])(i["default"],r["b"],r["c"],!1,null,"a1b801d6",null,!1,r["a"],void 0);e["default"]=o.exports},"5d43":function(t,e,n){"use strict";n.r(e);var r=n("7ea6"),i=n.n(r);for(var s in r)["default"].indexOf(s)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(s);e["default"]=i.a},"7ea6":function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=r(n("7ca3")),s=n("8fec"),a=n("8f59");function o(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,r)}return n}function d(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?o(Object(n),!0).forEach((function(e){(0,i.default)(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var u={components:{uniNavBar:function(){n.e("components/uni-nav-bar/uni-nav-bar").then(function(){return resolve(n("7858"))}.bind(null,n)).catch(n.oe)},Empty:function(){n.e("components/empty/empty").then(function(){return resolve(n("35cc"))}.bind(null,n)).catch(n.oe)}},data:function(){return{testValue:!0,addressList:[],formRouter:"",isActive:null,isEmpty:!1}},onShow:function(t){this.getAddressList(),t&&t.form&&(this.formRouter="",this.formRouter=t.form)},computed:d(d({},(0,a.mapState)(["addressBackUrl"])),{},{statusBarHeight:function(){return t.getSystemInfoSync().statusBarHeight+"px"}}),methods:d(d({},(0,a.mapMutations)(["setAddress"])),{},{goBack:function(){t.redirectTo({url:this.addressBackUrl})},getLableVal:function(t){switch(t){case"1":return"公司";case"2":return"家";case"3":return"学校";default:return"其他"}},getAddressList:function(){var e=this;this.testValue=!1,t.showLoading({title:"加载中",mask:!0}),(0,s.queryAddressBookList)().then((function(n){1===n.code&&(setTimeout((function(){t.hideLoading()}),100),e.testValue=!0,e.addressList=n.data,e.isEmpty=!0,e.addressList.map((function(t,n){1===t.isDefault&&(e.isActive=n)})))}))},addOrEdit:function(e,n){"新增"===e?t.redirectTo({url:"/pages/addOrEditAddress/addOrEditAddress"}):t.redirectTo({url:"/pages/addOrEditAddress/addOrEditAddress?type=编辑&id="+n.id})},choseAddress:function(e,n){if("/pages/order/index"!==this.addressBackUrl)return!1;t.redirectTo({url:"/pages/order/index?address="+JSON.stringify(n)}),this.setAddress(n)},getRadio:function(e,n){n.isDefault=1,this.isActive=e,(0,s.putAddressBookDefault)({id:n.id}).then((function(e){1===e.code&&t.showToast({title:"默认地址设置成功",duration:2e3,icon:"none"})}))}})};e.default=u}).call(this,n("df3c")["default"])},"8d7b":function(t,e,n){},"9fc2":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return s})),n.d(e,"a",(function(){return r}));var r={uniNavBar:function(){return n.e("components/uni-nav-bar/uni-nav-bar").then(n.bind(null,"7858"))},empty:function(){return n.e("components/empty/empty").then(n.bind(null,"35cc"))}},i=function(){var t=this,e=t.$createElement,n=(t._self._c,t.addressList&&t.addressList.length>0),r=n?t.__map(t.addressList,(function(e,n){var r=t.__get_orig(e),i=t.getLableVal(e.label);return{$orig:r,m0:i}})):null;t.$mp.data=Object.assign({},{$root:{g0:n,l0:r}})},s=[]}},[["1262","common/runtime","common/vendor"]]]);