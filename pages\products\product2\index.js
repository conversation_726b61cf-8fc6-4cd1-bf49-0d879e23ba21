import midManager from "../../../utils/midManager.js"

export default {
  data() {
    return {
      productInfo: {
        id: 2,
        name: '精选美味商品2',
        price: 35.90,
        image: '/static/logo_ruiji.png',
        description: '这是一款精心制作的美味商品，采用优质食材，口感丰富，营养均衡。'
      }
    }
  },

  onLoad(options) {
    console.log('商品2页面加载')
    if (options && options.mid) {
      midManager.setMid(options.mid)
    }
  },

  methods: {
    goBack() {
      uni.navigateBack({ delta: 1 })
    },

    goToOrder() {
      midManager.navigateTo('/pages/index/index')
    }
  }
}
