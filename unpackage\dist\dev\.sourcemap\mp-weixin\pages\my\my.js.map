{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/my.vue?2738", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/my.vue?ff1a", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/my.vue?afb0", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/my.vue?8a32", "uni-app:///pages/my/my.vue", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/my.vue?4a05", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/my/my.vue?7ca0"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "psersonUrl", "nick<PERSON><PERSON>", "gender", "phoneNumber", "recentOrdersList", "sumOrder", "amount", "number", "status", "scrollH", "pageInfo", "page", "pageSize", "total", "loadingText", "loading", "components", "HeadInfo", "OrderInfo", "OrderList", "filters", "getPhoneNum", "onLoad", "created", "onReady", "uni", "success", "methods", "statusWord", "getOvertime", "getList", "res", "go<PERSON>dd<PERSON>", "url", "goOrder", "oneOrderFun", "pages", "routeIndex", "delta", "quitClick", "goDetail", "dataAdd", "lower", "goBack"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,WAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACsD;AACL;AACsC;;;AAGvF;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,wEAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0MAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAA0xB,CAAgB,4wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACoD9yB;AACA;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAIA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA,kBACA,kCACA;IACA,gBACA;IACA,cACA;IAEA;EACA;EACAC;EACAC;IAAA;IACAC;MACAC;QACA;MACA;IACA;EACA;EACAC,yCACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAlB;QACAD;MACA;MACA;QACA;UACA,yDACAoB,iBACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAP;QACAQ;MACA;IACA;IACA;IACAC;MACA;MACAT;QACAQ;MACA;IACA;IACAE;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC,6BACA;kBAAA;gBAAA,EACA,EACA;gBAAA;gBAAA,OACA;cAAA;gBACA;kBACA;oBACAZ;sBACAa;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;IACA;IACAC;MACA;MACAf;QACAQ;MACA;IACA;IACAQ;MACA;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IACAC;MACAlB;QACAQ;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACpMA;AAAA;AAAA;AAAA;AAAq/C,CAAgB,m5CAAG,EAAC,C;;;;;;;;;;;ACAzgD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/my.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/my.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my.vue?vue&type=template&id=0be17cc6&scoped=true&\"\nvar renderjs\nimport script from \"./my.vue?vue&type=script&lang=js&\"\nexport * from \"./my.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my.vue?vue&type=style&index=0&id=0be17cc6&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0be17cc6\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/my.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=template&id=0be17cc6&scoped=true&\"", "var components\ntry {\n  components = {\n    uniNavBar: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-nav-bar/uni-nav-bar\" */ \"@/components/uni-nav-bar/uni-nav-bar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.recentOrdersList && _vm.recentOrdersList.length > 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=script&lang=js&\"", "<template>\n  <view>\n    <uni-nav-bar\n      @clickLeft=\"goBack\"\n      left-icon=\"back\"\n      leftIcon=\"arrowleft\"\n      title=\"地址管理\"\n      statusBar=\"true\"\n      fixed=\"true\"\n      color=\"#ffffff\"\n      backgroundColor=\"#ffc200\"\n    ></uni-nav-bar>\n\n    <view class=\"my-center\">\n      <!-- 头像展示部分 -->\n      <head\n        :psersonUrl=\"psersonUrl\"\n        :nickName=\"nickName\"\n        :gender=\"gender\"\n        :phoneNumber=\"phoneNumber\"\n        :getPhoneNum=\"getPhoneNum\"\n      ></head>\n\n      <view class=\"container\">\n        <!-- 地址和历史订单 -->\n        <order-info @goAddress=\"goAddress\" @goOrder=\"goOrder\"></order-info>\n        <!-- 最近订单 -->\n        <!-- 最近订单title -->\n        <view\n          class=\"recent\"\n          v-if=\"recentOrdersList && recentOrdersList.length > 0\"\n        >\n          <text class=\"order_line\">最近订单</text>\n        </view>\n        <order-list\n          :scrollH=\"scrollH\"\n          @lower=\"lower\"\n          @goDetail=\"goDetail\"\n          @oneOrderFun=\"oneOrderFun\"\n          @getOvertime=\"getOvertime\"\n          @statusWord=\"statusWord\"\n          @numes=\"numes\"\n          :loading=\"loading\"\n          :loadingText=\"loadingText\"\n          :recentOrdersList=\"recentOrdersList\"\n        ></order-list>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { getOrderPage, repetitionOrder, delShoppingCart } from \"../api/api.js\";\nimport { mapMutations } from \"vuex\";\nimport { statusWord, getOvertime } from \"@/utils/index.js\";\n\nimport HeadInfo from \"./components/headInfo.vue\"; //头部\nimport OrderInfo from \"./components/orderInfo.vue\"; //地址\nimport OrderList from \"./components/orderList.vue\"; //最近订单\nexport default {\n  data() {\n    return {\n      psersonUrl: \"../../static/btn_waiter_sel.png\",\n      nickName: \"\",\n      gender: \"0\",\n      phoneNumber: \"18500557668\",\n      recentOrdersList: [],\n      sumOrder: {\n        amount: 0,\n        number: 0,\n      },\n      status: \"\",\n      scrollH: 0,\n      pageInfo: {\n        page: 1,\n        pageSize: 10,\n        total: 0,\n      },\n      loadingText: \"\",\n      loading: false,\n    };\n  },\n  components: {\n    HeadInfo,\n    OrderInfo,\n    OrderList,\n  },\n  filters: {\n    getPhoneNum(str) {\n      return str.replace(/\\-/g, \"\");\n    },\n  },\n  onLoad() {\n    this.psersonUrl =\n      this.$store.state.baseUserInfo &&\n      this.$store.state.baseUserInfo.avatarUrl;\n    this.nickName =\n      this.$store.state.baseUserInfo && this.$store.state.baseUserInfo.nickName;\n    this.gender =\n      this.$store.state.baseUserInfo && this.$store.state.baseUserInfo.gender;\n\n    this.getList();\n  },\n  created() {},\n  onReady() {\n    uni.getSystemInfo({\n      success: (res) => {\n        this.scrollH = res.windowHeight - uni.upx2px(100);\n      },\n    });\n  },\n  methods: {\n    ...mapMutations([\"setAddressBackUrl\"]),\n    statusWord(obj) {\n      return statusWord(obj.status, obj.time);\n    },\n    getOvertime(time) {\n      return getOvertime(time);\n    },\n    // 获取列表数据\n    getList() {\n      const params = {\n        pageSize: 10,\n        page: this.pageInfo.page,\n      };\n      getOrderPage(params).then((res) => {\n        if (res.code === 1) {\n          this.recentOrdersList = this.recentOrdersList.concat(\n            res.data.records\n          );\n          this.pageInfo.total = res.data.total;\n          this.loadingText = \"\";\n          this.loading = false;\n        }\n      });\n    },\n    // 去地址页面\n    goAddress() {\n      this.setAddressBackUrl(\"/pages/my/my\");\n      // TODO\n      uni.redirectTo({\n        url: \"/pages/address/address?form=\" + \"my\",\n      });\n    },\n    // 去历史订单页面\n    goOrder() {\n      // TODO\n      uni.navigateTo({\n        url: \"/pages/historyOrder/historyOrder\",\n      });\n    },\n    async oneOrderFun(id) {\n      let pages = getCurrentPages();\n      let routeIndex = pages.findIndex(\n        (item) => item.route === \"pages/index/index\"\n      );\n      // 先清空购物车\n      await delShoppingCart();\n      repetitionOrder(id).then((res) => {\n        if (res.code === 1) {\n          uni.navigateBack({\n            delta: routeIndex > -1 ? pages.length - routeIndex : 1,\n          });\n        }\n      });\n    },\n    quitClick() {},\n    // 去详情页面\n    goDetail(id) {\n      this.setAddressBackUrl(\"/pages/my/my\");\n      uni.redirectTo({\n        url: \"/pages/details/index?orderId=\" + id,\n      });\n    },\n    dataAdd() {\n      const pages = Math.ceil(this.pageInfo.total / 10); //计算总页数\n      if (this.pageInfo.page === pages) {\n        this.loadingText = \"没有更多了\";\n        this.loading = true;\n      } else {\n        this.pageInfo.page++;\n        this.getList();\n      }\n    },\n\n    lower() {\n      this.loadingText = \"数据加载中...\";\n      this.loading = true;\n      this.dataAdd();\n    },\n    goBack() {\n      uni.redirectTo({\n        url: \"/pages/index/index\",\n      });\n    },\n  },\n};\n</script>\n<style lang=\"scss\" scoped>\n.my-center {\n  background: #f6f6f6;\n  height: 100%;\n\n  .container {\n    margin-top: 20rpx;\n    height: calc(100% - 194rpx);\n  }\n}\n::v-deep .uni-navbar--border {\n  border-width: 0 !important;\n}\n</style>\n", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=style&index=0&id=0be17cc6&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=style&index=0&id=0be17cc6&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753343924471\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}