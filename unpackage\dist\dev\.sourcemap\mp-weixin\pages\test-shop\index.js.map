{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/test-shop/index.vue?60a4", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/test-shop/index.vue?6e58", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/test-shop/index.vue?6a8a", "uni-app:///pages/test-shop/index.vue", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/test-shop/index.vue?19f1", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/test-shop/index.vue?801c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "currentMid", "computed", "currentConfig", "onLoad", "methods", "testMid", "midManager", "console", "uni", "title", "icon", "goToIndex", "url", "showHistory", "content", "showCancel"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACqC;;;AAGzF;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6xB,CAAgB,+wBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0CjzB;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MACA;QACAC;QACA;QACAC;MACA;QACAD;QACA;QACAC;MACA;MAEAC;QACAC;QACAC;MACA;IACA;IAEAC;MACA;MACA;MACAH;QACAI;MACA;IACA;IAEA;IACAC;MACA;MACAL;QACAC;QACAK;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClGA;AAAA;AAAA;AAAA;AAAmpC,CAAgB,4lCAAG,EAAC,C;;;;;;;;;;;ACAvqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/test-shop/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/test-shop/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3be3ef2b&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=3be3ef2b&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3be3ef2b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/test-shop/index.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=3be3ef2b&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"test-container\">\n    <view class=\"header\">\n      <text class=\"title\">店铺配置测试页面</text>\n    </view>\n    \n    <view class=\"current-config\">\n      <text class=\"section-title\">当前配置 (mid: {{ currentMid || '无' }})</text>\n      <view class=\"config-item\">\n        <text class=\"label\">店铺名称:</text>\n        <text class=\"value\">{{ currentConfig.shopName }}</text>\n      </view>\n      <view class=\"config-item\">\n        <text class=\"label\">店铺地址:</text>\n        <text class=\"value\">{{ currentConfig.shopAddress }}</text>\n      </view>\n      <view class=\"config-item\">\n        <text class=\"label\">联系电话:</text>\n        <text class=\"value\">{{ currentConfig.phone }}</text>\n      </view>\n    </view>\n    \n    <view class=\"test-buttons\">\n      <text class=\"section-title\">测试不同 mid</text>\n      <view class=\"button-row\">\n        <button class=\"test-btn\" @click=\"testMid('')\">默认 (无mid)</button>\n        <button class=\"test-btn\" @click=\"testMid('1')\">mid=1</button>\n      </view>\n      <view class=\"button-row\">\n        <button class=\"test-btn\" @click=\"testMid('2')\">mid=2</button>\n        <button class=\"test-btn\" @click=\"testMid('3')\">mid=3</button>\n      </view>\n    </view>\n    \n    <view class=\"navigation\">\n      <button class=\"nav-btn\" @click=\"goToIndex\">返回首页</button>\n      <button class=\"history-btn\" @click=\"showHistory\">查看历史</button>\n    </view>\n  </view>\n</template>\n\n<script>\nimport midManager from \"../../utils/midManager.js\"\nimport { getShopConfigByMid } from \"../../utils/shopConfig.js\"\n\nexport default {\n  data() {\n    return {\n      currentMid: null\n    }\n  },\n  \n  computed: {\n    currentConfig() {\n      return getShopConfigByMid(this.currentMid)\n    }\n  },\n  \n  onLoad() {\n    this.currentMid = midManager.getMid()\n  },\n  \n  methods: {\n    testMid(mid) {\n      if (mid) {\n        midManager.setMid(mid)\n        this.currentMid = mid\n        console.log('测试页面设置mid:', mid)\n      } else {\n        midManager.clearMid()\n        this.currentMid = null\n        console.log('测试页面清除mid')\n      }\n\n      uni.showToast({\n        title: `已切换到 ${mid || '默认'} 配置`,\n        icon: 'success'\n      })\n    },\n\n    goToIndex() {\n      // 使用 navigateTo 而不是 redirectTo，这样可以保持参数\n      const url = this.currentMid ? `/pages/index/index?mid=${this.currentMid}` : '/pages/index/index'\n      uni.navigateTo({\n        url: url\n      })\n    },\n\n    // 查看历史记录\n    showHistory() {\n      const history = midManager.getMidHistory()\n      uni.showModal({\n        title: 'Mid历史记录',\n        content: history.length > 0 ? history.join(', ') : '暂无历史记录',\n        showCancel: false\n      })\n    }\n  }\n}\n</script>\n\n<style scoped>\n.test-container {\n  padding: 20rpx;\n  background-color: #f5f5f5;\n  min-height: 100vh;\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.title {\n  font-size: 36rpx;\n  font-weight: bold;\n  color: #333;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #666;\n  margin-bottom: 20rpx;\n  display: block;\n}\n\n.current-config {\n  background-color: white;\n  padding: 30rpx;\n  border-radius: 10rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.config-item {\n  display: flex;\n  margin-bottom: 20rpx;\n  align-items: center;\n}\n\n.label {\n  font-size: 28rpx;\n  color: #666;\n  width: 160rpx;\n  flex-shrink: 0;\n}\n\n.value {\n  font-size: 28rpx;\n  color: #333;\n  flex: 1;\n}\n\n.test-buttons {\n  background-color: white;\n  padding: 30rpx;\n  border-radius: 10rpx;\n  margin-bottom: 40rpx;\n  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);\n}\n\n.button-row {\n  display: flex;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.test-btn {\n  flex: 1;\n  margin: 0 10rpx;\n  padding: 20rpx;\n  background-color: #007aff;\n  color: white;\n  border: none;\n  border-radius: 8rpx;\n  font-size: 26rpx;\n}\n\n.test-btn:active {\n  background-color: #0056cc;\n}\n\n.navigation {\n  text-align: center;\n}\n\n.nav-btn {\n  width: 300rpx;\n  padding: 25rpx;\n  background-color: #ff6b35;\n  color: white;\n  border: none;\n  border-radius: 8rpx;\n  font-size: 30rpx;\n}\n\n.nav-btn:active {\n  background-color: #e55a2b;\n}\n\n.history-btn {\n  width: 300rpx;\n  padding: 25rpx;\n  background-color: #28a745;\n  color: white;\n  border: none;\n  border-radius: 8rpx;\n  font-size: 30rpx;\n  margin-top: 20rpx;\n}\n\n.history-btn:active {\n  background-color: #218838;\n}\n</style>\n", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=3be3ef2b&scoped=true&lang=css&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=3be3ef2b&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753346811444\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}