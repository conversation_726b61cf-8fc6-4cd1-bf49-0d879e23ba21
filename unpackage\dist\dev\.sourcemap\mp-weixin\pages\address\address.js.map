{"version": 3, "sources": ["uni-app:///main.js", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/address/address.vue?5623", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/address/address.vue?1b6c", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/address/address.vue?9a80", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/address/address.vue?c033", "uni-app:///pages/address/address.vue", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/address/address.vue?6904", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/pages/address/address.vue?fbbc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "uniNavBar", "Empty", "data", "testValue", "addressList", "formRouter", "isActive", "isEmpty", "onShow", "computed", "statusBarHeight", "methods", "goBack", "uni", "url", "getLableVal", "getAddressList", "title", "mask", "setTimeout", "addOrEdit", "item", "<PERSON><PERSON><PERSON><PERSON>", "getRadio", "id", "duration", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0MAEN;AACP,KAAK;AACL;AACA,aAAa,sKAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAA+xB,CAAgB,ixBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACkGnzB;AACA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAGA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;EACA;EACAC,0CACA;IACAC;MACA;IACA;EAAA,EACA;EACAC,yCACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IACAC;MAAA;MACA;MACAH;QAAAI;QAAAC;MAAA;MACA;QACA;UACAC;YACAN;UACA;UACA;UACA;UACA;UACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IAEAO;MACA;MACA;QACA;QACAP;UACAC;QACA;MACA;QACA;QACAD;UACAC,KACA,mDACA,OACA,MACA,QACAO;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;MACAT;QACAC;MACA;MACA;IACA;IACAS;MACA;MACAF;MACA;MACA;QAAAG;MAAA;QACA;UACAX;YACAI;YACAQ;YACAC;UACA;QACA;MACA;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACpNA;AAAA;AAAA;AAAA;AAA0/C,CAAgB,w5CAAG,EAAC,C;;;;;;;;;;;ACA9gD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/address/address.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/address/address.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./address.vue?vue&type=template&id=db675620&scoped=true&\"\nvar renderjs\nimport script from \"./address.vue?vue&type=script&lang=js&\"\nexport * from \"./address.vue?vue&type=script&lang=js&\"\nimport style0 from \"./address.vue?vue&type=style&index=0&id=db675620&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"db675620\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/address/address.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=template&id=db675620&scoped=true&\"", "var components\ntry {\n  components = {\n    uniNavBar: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-nav-bar/uni-nav-bar\" */ \"@/components/uni-nav-bar/uni-nav-bar.vue\"\n      )\n    },\n    empty: function () {\n      return import(\n        /* webpackChunkName: \"components/empty/empty\" */ \"@/components/empty/empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.addressList && _vm.addressList.length > 0\n  var l0 = g0\n    ? _vm.__map(_vm.addressList, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var m0 = _vm.getLableVal(item.label)\n        return {\n          $orig: $orig,\n          m0: m0,\n        }\n      })\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"customer-box\">\n    <uni-nav-bar\n      @clickLeft=\"goBack\"\n      left-icon=\"back\"\n      leftIcon=\"arrowleft\"\n      title=\"地址管理\"\n      statusBar=\"true\"\n      fixed=\"true\"\n      color=\"#ffffff\"\n      backgroundColor=\"#333333\"\n    ></uni-nav-bar>\n    <view\n      class=\"address\"\n      :style=\"{\n        height: `calc(100% - 136rpx - ${statusBarHeight} - 44px - 20rpx)`,\n      }\"\n    >\n      <view\n        v-if=\"addressList && addressList.length > 0\"\n        class=\"address_content\"\n      >\n        <!-- address列表 -->\n        <view\n          class=\"address_liests\"\n          v-for=\"(item, index) in addressList\"\n          :key=\"index\"\n        >\n          <!-- 上部 -->\n          <view class=\"list_item_top\" @click.stop=\"choseAddress(index, item)\">\n            <!-- 左边 -->\n            <view class=\"item_left\">\n              <!-- 地址 -->\n              <view class=\"details\">\n                <text class=\"tag\" :class=\"'tag' + item.label\">{{\n                  getLableVal(item.label)\n                }}</text>\n                <text class=\"address_word\"\n                  >{{ item.provinceName }}{{ item.cityName\n                  }}{{ item.districtName }}{{ item.detail }}</text\n                >\n              </view>\n              <!-- 性别及手机号 -->\n              <view class=\"sale\">\n                <text class=\"name\">{{\n                  item.sex === \"0\"\n                    ? item.consignee + \" 先生\"\n                    : item.consignee + \" 女士\"\n                }}</text>\n                <text class=\"num\">{{ item.phone }}</text>\n              </view>\n            </view>\n            <!-- 右边 -->\n            <view class=\"item_right\">\n              <image\n                @click.stop=\"addOrEdit('编辑', item)\"\n                class=\"edit\"\n                src=\"../../static/edit.png\"\n              ></image>\n            </view>\n          </view>\n          <!-- 下部 -->\n          <view class=\"list_item_bottom\">\n            <label class=\"radio\" @click.stop=\"getRadio(index, item)\">\n              <radio\n                class=\"item_radio\"\n                v-if=\"testValue\"\n                color=\"#FFC200\"\n                :value=\"item.id\"\n                :checked=\"item.isDefault === 1 && isActive === index\"\n                @click.stop=\"getRadio(index, item)\"\n              />设为默认地址</label\n            >\n          </view>\n        </view>\n      </view>\n      <!-- 无地址展示 -->\n      <empty\n        v-if=\"isEmpty\"\n        boxHeight=\"100%\"\n        textLabel=\"一个地址都没有哦\"\n      ></empty>\n      <view class=\"add_address\">\n        <button\n          class=\"add_btn\"\n          type=\"primary\"\n          plain=\"true\"\n          @click=\"addOrEdit('新增')\"\n        >\n          <text class=\"add-icon\">+</text>\n          新增收货地址\n        </button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport { queryAddressBookList, putAddressBookDefault } from \"../api/api.js\";\nimport { mapState, mapMutations } from \"vuex\";\nimport uniNavBar from \"@/components/uni-nav-bar/uni-nav-bar.vue\";\nimport Empty from \"@/components/empty/empty\";\nexport default {\n  components: {\n    uniNavBar,\n    Empty,\n  },\n  data() {\n    return {\n      testValue: true,\n      addressList: [],\n      formRouter: \"\",\n      isActive: null,\n      isEmpty: false,\n    };\n  },\n  onShow(options) {\n    this.getAddressList();\n    if (options && options.form) {\n      this.formRouter = \"\";\n      this.formRouter = options.form;\n    }\n  },\n  computed: {\n    ...mapState([\"addressBackUrl\"]),\n    statusBarHeight() {\n      return uni.getSystemInfoSync().statusBarHeight + \"px\";\n    },\n  },\n  methods: {\n    ...mapMutations([\"setAddress\"]),\n    goBack() {\n      uni.redirectTo({\n        url: this.addressBackUrl,\n      });\n    },\n    getLableVal(item) {\n      switch (item) {\n        case \"1\":\n          return \"公司\";\n        case \"2\":\n          return \"家\";\n        case \"3\":\n          return \"学校\";\n        default:\n          return \"其他\";\n      }\n    },\n    getAddressList() {\n      this.testValue = false;\n      uni.showLoading({ title: \"加载中\", mask: true });\n      queryAddressBookList().then((res) => {\n        if (res.code === 1) {\n          setTimeout(function () {\n            uni.hideLoading();\n          }, 100);\n          this.testValue = true;\n          this.addressList = res.data;\n          this.isEmpty = true;\n          this.addressList.map((val, index) => {\n            if (val.isDefault === 1) {\n              this.isActive = index;\n            }\n          });\n        }\n      });\n    },\n\n    addOrEdit(type, item) {\n      // 编辑与新增\n      if (type === \"新增\") {\n        // TODO\n        uni.redirectTo({\n          url: \"/pages/addOrEditAddress/addOrEditAddress\",\n        });\n      } else {\n        // TODO\n        uni.redirectTo({\n          url:\n            \"/pages/addOrEditAddress/addOrEditAddress?type=\" +\n            \"编辑\" +\n            \"&\" +\n            \"id=\" +\n            item.id,\n        });\n      }\n    },\n    // 点击整体设置为默认地址并返填订单页面\n    choseAddress(e, item) {\n      if (this.addressBackUrl !== \"/pages/order/index\") {\n        return false;\n      }\n      uni.redirectTo({\n        url: \"/pages/order/index?address=\" + JSON.stringify(item),\n      });\n      this.setAddress(item);\n    },\n    getRadio(index, item) {\n      // // 提供默认接口\n      item.isDefault = 1;\n      this.isActive = index;\n      putAddressBookDefault({ id: item.id }).then((res) => {\n        if (res.code === 1) {\n          uni.showToast({\n            title: \"默认地址设置成功\",\n            duration: 2000,\n            icon: \"none\",\n          });\n        }\n      });\n    },\n  },\n};\n</script>\n\n<style lang=\"scss\" scoped>\n.address {\n  width: 750rpx;\n  .address_content {\n    margin: 0 20rpx;\n    padding-bottom: 20rpx;\n    height: 100%;\n    overflow-y: auto;\n    .address_liests {\n      width: 100%;\n      height: 256rpx;\n      opacity: 1;\n      background: #ffffff;\n      border-radius: 12rpx;\n      display: flex;\n      display: flex;\n      flex-direction: column;\n      margin-top: 20rpx;\n      padding: 0 28rpx 0 12rpx;\n      box-sizing: border-box;\n      // 上部\n      .list_item_top {\n        flex: 1;\n        width: 100%;\n        height: 100%;\n        display: flex;\n        // 左边\n        .item_left {\n          flex: 1;\n          overflow: hidden;\n          margin-left: 12rpx;\n          // 地址\n          .details {\n            margin-top: 42rpx;\n            display: flex;\n            height: 40rpx;\n            line-height: 40rpx;\n\n            // 地址描述\n            .address_word {\n              flex: 1;\n              font-size: 28rpx;\n              font-family: PingFangSC, PingFangSC-Regular;\n              font-weight: 400;\n              text-align: left;\n              color: #333333;\n              overflow: hidden;\n              text-overflow: ellipsis;\n              white-space: nowrap;\n            }\n            // 不同标签展示不同背景色\n            .active {\n              background: #fef8e7;\n            }\n          }\n          // 姓名及手机号\n          .sale {\n            margin-top: 20rpx;\n            .name,\n            .num {\n              height: 40rpx;\n              opacity: 1;\n              font-size: 28rpx;\n              font-family: PingFangSC, PingFangSC-Regular;\n              font-weight: 400;\n              text-align: left;\n              color: #999999;\n              line-height: 40rpx;\n              letter-spacing: 0px;\n            }\n            .num {\n              margin-left: 20rpx;\n            }\n          }\n        }\n        // 右边--编辑\n        .item_right {\n          width: 100rpx;\n          height: 100rpx;\n          line-height: 1;\n          text-align: right;\n          padding-right: 18rpx;\n          .edit {\n            width: 32rpx;\n            height: 32rpx;\n            padding: 24rpx;\n            margin-top: 50rpx;\n            margin-left: 20rpx;\n          }\n        }\n      }\n      // 下部\n      .list_item_bottom {\n        height: 80rpx;\n        line-height: 80rpx;\n        border-top: 1px solid #efefef;\n        .radio {\n          margin-left: 8rpx;\n          opacity: 1;\n          font-size: 26rpx;\n          font-family: PingFangSC, PingFangSC-Regular;\n          font-weight: 400;\n          text-align: left;\n          color: #333333;\n          .item_radio {\n            transform: scale(0.7);\n          }\n        }\n      }\n    }\n  }\n  // 暂无地址\n  .no_address {\n    margin: 0 auto;\n    height: 50rpx;\n    .no_word {\n      display: block;\n      text-align: center;\n      font-size: 32rpx;\n    }\n  }\n  .add_address {\n    position: fixed;\n    bottom: 40rpx;\n    left: 20rpx;\n    right: 20rpx;\n    margin: 0 auto;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    .add_btn {\n      width: 100%;\n      height: 86rpx;\n      line-height: 86rpx;\n      border-radius: 8rpx;\n      background: #ffc200;\n      border: 1px solid #ffc200;\n      opacity: 1;\n      font-size: 30rpx;\n      font-family: PingFangSC, PingFangSC-Medium;\n      font-weight: 600;\n      text-align: center;\n      color: #333333;\n      letter-spacing: 0px;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      .add-icon {\n        font-size: 32rpx;\n        margin-right: 8rpx;\n        margin-bottom: 4rpx;\n      }\n      .img_btn {\n        width: 44rpx;\n        height: 44rpx;\n        vertical-align: middle;\n        margin-bottom: 8rpx;\n      }\n    }\n  }\n}\n.customer-box {\n  height: 100vh;\n}\n</style>\n", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=style&index=0&id=db675620&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./address.vue?vue&type=style&index=0&id=db675620&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753343924742\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}