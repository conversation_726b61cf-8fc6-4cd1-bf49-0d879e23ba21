<template>
  <view class="test-container">
    <view class="header">
      <text class="title">店铺配置测试页面</text>
    </view>
    
    <view class="current-config">
      <text class="section-title">当前配置 (mid: {{ currentMid || '无' }})</text>
      <view class="config-item">
        <text class="label">店铺名称:</text>
        <text class="value">{{ currentConfig.shopName }}</text>
      </view>
      <view class="config-item">
        <text class="label">店铺地址:</text>
        <text class="value">{{ currentConfig.shopAddress }}</text>
      </view>
      <view class="config-item">
        <text class="label">联系电话:</text>
        <text class="value">{{ currentConfig.phone }}</text>
      </view>
    </view>
    
    <view class="test-buttons">
      <text class="section-title">测试不同 mid</text>
      <view class="button-row">
        <button class="test-btn" @click="testMid('')">默认 (无mid)</button>
        <button class="test-btn" @click="testMid('1')">mid=1</button>
      </view>
      <view class="button-row">
        <button class="test-btn" @click="testMid('2')">mid=2</button>
        <button class="test-btn" @click="testMid('3')">mid=3</button>
      </view>
    </view>
    
    <view class="navigation">
      <button class="nav-btn" @click="goToIndex">返回首页</button>
    </view>
  </view>
</template>

<script>
import midManager from "../../utils/midManager.js"
import { getShopConfigByMid } from "../../utils/shopConfig.js"

export default {
  data() {
    return {
      currentMid: null
    }
  },
  
  computed: {
    currentConfig() {
      return getShopConfigByMid(this.currentMid)
    }
  },
  
  onLoad() {
    this.currentMid = midManager.getMid()
  },
  
  methods: {
    testMid(mid) {
      if (mid) {
        midManager.setMid(mid)
        this.currentMid = mid
      } else {
        midManager.clearMid()
        this.currentMid = null
      }
      
      uni.showToast({
        title: `已切换到 ${mid || '默认'} 配置`,
        icon: 'success'
      })
    },
    
    goToIndex() {
      uni.redirectTo({
        url: '/pages/index/index'
      })
    }
  }
}
</script>

<style scoped>
.test-container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #666;
  margin-bottom: 20rpx;
  display: block;
}

.current-config {
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.config-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: center;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.value {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.test-buttons {
  background-color: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.1);
}

.button-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.test-btn {
  flex: 1;
  margin: 0 10rpx;
  padding: 20rpx;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 26rpx;
}

.test-btn:active {
  background-color: #0056cc;
}

.navigation {
  text-align: center;
}

.nav-btn {
  width: 300rpx;
  padding: 25rpx;
  background-color: #ff6b35;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 30rpx;
}

.nav-btn:active {
  background-color: #e55a2b;
}
</style>
