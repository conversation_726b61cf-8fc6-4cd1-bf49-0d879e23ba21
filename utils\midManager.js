/**
 * Mid参数管理工具
 * 用于在整个应用中持久化保存和获取mid参数
 */

const MID_STORAGE_KEY = 'persistentMid'

export const midManager = {
  /**
   * 设置mid参数
   * @param {string|number} mid - mid参数值
   */
  setMid(mid) {
    if (mid !== null && mid !== undefined) {
      try {
        const midStr = mid.toString()
        const currentMid = this.getMid()

        // 只有当 mid 真正发生变化时才保存
        if (currentMid !== midStr) {
          uni.setStorageSync(MID_STORAGE_KEY, midStr)
          console.log('Mid参数已保存:', midStr, '(原值:', currentMid, ')')

          // 触发自定义事件通知 mid 变化
          uni.$emit('midChanged', {
            oldMid: currentMid,
            newMid: midStr
          })
        }
      } catch (error) {
        console.error('保存Mid参数失败:', error)
      }
    }
  },

  /**
   * 获取mid参数
   * @returns {string|null} - 返回保存的mid参数，如果没有则返回null
   */
  getMid() {
    try {
      const mid = uni.getStorageSync(MID_STORAGE_KEY)
      return mid || null
    } catch (error) {
      console.error('获取Mid参数失败:', error)
      return null
    }
  },

  /**
   * 清除mid参数
   */
  clearMid() {
    try {
      const currentMid = this.getMid()
      if (currentMid) {
        uni.removeStorageSync(MID_STORAGE_KEY)
        console.log('Mid参数已清除，原值:', currentMid)

        // 触发自定义事件通知 mid 清除
        uni.$emit('midChanged', {
          oldMid: currentMid,
          newMid: null
        })
      }
    } catch (error) {
      console.error('清除Mid参数失败:', error)
    }
  },

  /**
   * 检查是否存在mid参数
   * @returns {boolean} - 如果存在mid参数返回true，否则返回false
   */
  hasMid() {
    return !!this.getMid()
  },

  /**
   * 为URL添加mid参数
   * @param {string} url - 原始URL
   * @returns {string} - 添加了mid参数的URL
   */
  addMidToUrl(url) {
    const mid = this.getMid()
    if (!mid) {
      return url
    }

    const separator = url.includes('?') ? '&' : '?'
    return `${url}${separator}mid=${mid}`
  },

  /**
   * 跳转到指定页面并自动添加mid参数
   * @param {string} url - 目标页面URL
   * @param {object} options - 跳转选项
   */
  navigateTo(url, options = {}) {
    const urlWithMid = this.addMidToUrl(url)
    uni.navigateTo({
      url: urlWithMid,
      ...options
    })
  },

  /**
   * 重定向到指定页面并自动添加mid参数
   * @param {string} url - 目标页面URL
   * @param {object} options - 重定向选项
   */
  redirectTo(url, options = {}) {
    const urlWithMid = this.addMidToUrl(url)
    uni.redirectTo({
      url: urlWithMid,
      ...options
    })
  },

  /**
   * 强制更新mid参数（即使值相同也会触发事件）
   * @param {string|number} mid - mid参数值
   */
  forceUpdateMid(mid) {
    try {
      const currentMid = this.getMid()
      const midStr = mid ? mid.toString() : null

      if (midStr) {
        uni.setStorageSync(MID_STORAGE_KEY, midStr)
      } else {
        uni.removeStorageSync(MID_STORAGE_KEY)
      }

      console.log('强制更新Mid参数:', currentMid, '->', midStr)

      // 总是触发事件
      uni.$emit('midChanged', {
        oldMid: currentMid,
        newMid: midStr
      })
    } catch (error) {
      console.error('强制更新Mid参数失败:', error)
    }
  },

  /**
   * 获取mid参数的历史记录（可选功能）
   * @returns {array} - mid历史记录数组
   */
  getMidHistory() {
    try {
      const history = uni.getStorageSync('midHistory') || []
      return history
    } catch (error) {
      console.error('获取Mid历史记录失败:', error)
      return []
    }
  },

  /**
   * 添加mid到历史记录
   * @param {string} mid - mid参数值
   */
  addToHistory(mid) {
    if (!mid) return

    try {
      let history = this.getMidHistory()
      const midStr = mid.toString()

      // 移除重复项
      history = history.filter(item => item !== midStr)

      // 添加到开头
      history.unshift(midStr)

      // 限制历史记录数量（最多保存10个）
      if (history.length > 10) {
        history = history.slice(0, 10)
      }

      uni.setStorageSync('midHistory', history)
    } catch (error) {
      console.error('添加Mid历史记录失败:', error)
    }
  }
}

export default midManager
