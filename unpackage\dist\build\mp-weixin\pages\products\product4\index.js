(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/products/product4/index"],{"992d":function(n,t,e){"use strict";(function(n,t){var a=e("47a9");e("b759");a(e("3240"));var c=a(e("ac47"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(c.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},ac47:function(n,t,e){"use strict";e.r(t);var a=e("d91a"),c=e("2525");for(var u in c)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return c[n]}))}(u);e("e9d2");var r=e("828b"),o=Object(r["a"])(c["default"],a["b"],a["c"],!1,null,"ef3af2e6",null,!1,a["a"],void 0);t["default"]=o.exports},d91a:function(n,t,e){"use strict";e.d(t,"b",(function(){return a})),e.d(t,"c",(function(){return c})),e.d(t,"a",(function(){}));var a=function(){var n=this.$createElement;this._self._c},c=[]}},[["992d","common/runtime","common/vendor"]]]);