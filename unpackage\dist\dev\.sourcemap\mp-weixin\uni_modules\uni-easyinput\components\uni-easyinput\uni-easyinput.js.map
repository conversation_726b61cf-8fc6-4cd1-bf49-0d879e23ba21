{"version": 3, "sources": ["webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?a480", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?a5f3", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?c90a", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?abc8", "uni-app:///uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?d770", "webpack:///D:/桌面/diancan/project-rjwm-weixin-uniapp-develop-wsy 2/project-rjwm-weixin-uniapp-develop-wsy/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue?29b6"], "names": ["classess", "style", "name", "emits", "model", "prop", "event", "options", "virtualHost", "inject", "form", "from", "default", "formItem", "props", "value", "modelValue", "type", "clearable", "autoHeight", "placeholder", "placeholder<PERSON><PERSON><PERSON>", "focus", "disabled", "maxlength", "confirmType", "clearSize", "inputBorder", "prefixIcon", "suffixIcon", "trim", "cursorSpacing", "passwordIcon", "primaryColor", "styles", "color", "backgroundColor", "disableColor", "borderColor", "errorMessage", "data", "focused", "val", "showMsg", "border", "isFirstBorder", "showClearIcon", "showPassword", "focusShow", "localMsg", "isEnter", "computed", "isVal", "msg", "inputMaxlength", "boxStyle", "inputContentClass", "inputContentStyle", "inputStyle", "watch", "created", "mounted", "methods", "init", "onClickIcon", "onEyes", "onInput", "onFocus", "_Focus", "onBlur", "_Blur", "onConfirm", "onClear", "onkeyboardheightchange", "trimStr"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0H;AAC1H;AACiE;AACL;AACc;;;AAG1E;AAC2M;AAC3M,gBAAgB,6MAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,wFAAM;AACR,EAAE,iGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAqyB,CAAgB,uxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC8EzzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACA;EACA;IACA;IACA;MACAA;IACA;EACA;EACA;AACA;AAEA;EACA;EACA;IACA;IACAC;EACA;EACA;AACA;AAAA,gBACA;EACAC;EACAC;EACAC;IACAC;IACAC;EACA;EACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAE;IACAZ;IACAa;IACAC;IACAC;MACAA;MACAL;IACA;IACAM;MACAD;MACAL;IACA;IACAO;MACAF;MACAL;IACA;IACAQ;MACAH;MACAL;IACA;IACAS;IACAC;MACAL;MACAL;IACA;IACAW;MACAN;MACAL;IACA;IACAY;MACAP;MACAL;IACA;IACAa;MACAR;MACAL;IACA;IACAc;MACAT;MACAL;IACA;IACAe;MACAV;MACAL;IACA;IACAgB;MACAX;MACAL;IACA;IACAiB;MACAZ;MACAL;IACA;IACAkB;MACAb;MACAL;IACA;IACAmB;MACAd;MACAL;IACA;IACAoB;MACAf;MACAL;IACA;IACAqB;MACAhB;MACAL;IACA;IACAsB;MACAjB;MACAL;QACA;UACAuB;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MACAtB;MACAL;IACA;EACA;EACA4B;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IAEA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA5C;MACA;IACA;IACAC;MACA;IACA;IACAM;MAAA;MACA;QACA;QACA;MACA;IACA;EACA;EACAsC;IAAA;IACA;IACA;IACA;MACA;QACA;MACA;IACA;EACA;EACAC;IAAA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;QACA;UACAnD;QACA;QACA;UACAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAoD;MAAA;MACA;QACA;MACA;MACA;IACA;IAEAC;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAC;MACA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AClfA;AAAA;AAAA;AAAA;AAAw+C,CAAgB,s4CAAG,EAAC,C;;;;;;;;;;;ACA5/C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-easyinput.vue?vue&type=template&id=abe12412&\"\nvar renderjs\nimport script from \"./uni-easyinput.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-easyinput.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-easyinput.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.vue\"\nexport default component.exports", "export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-easyinput.vue?vue&type=template&id=abe12412&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-icons/uni-icons\" */ \"@/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-easyinput.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-easyinput.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"uni-easyinput\" :class=\"{ 'uni-easyinput-error': msg }\" :style=\"boxStyle\">\n\t\t<view class=\"uni-easyinput__content\" :class=\"inputContentClass\" :style=\"inputContentStyle\">\n\t\t\t<uni-icons v-if=\"prefixIcon\" class=\"content-clear-icon\" :type=\"prefixIcon\" color=\"#c0c4cc\" @click=\"onClickIcon('prefix')\" size=\"22\"></uni-icons>\n\t\t\t<textarea\n\t\t\t\tv-if=\"type === 'textarea'\"\n\t\t\t\tclass=\"uni-easyinput__content-textarea\"\n\t\t\t\t:class=\"{ 'input-padding': inputBorder }\"\n\t\t\t\t:name=\"name\"\n\t\t\t\t:value=\"val\"\n\t\t\t\t:placeholder=\"placeholder\"\n\t\t\t\t:placeholderStyle=\"placeholderStyle\"\n\t\t\t\t:disabled=\"disabled\"\n\t\t\t\tplaceholder-class=\"uni-easyinput__placeholder-class\"\n\t\t\t\t:maxlength=\"inputMaxlength\"\n\t\t\t\t:focus=\"focused\"\n\t\t\t\t:autoHeight=\"autoHeight\"\n\t\t\t\t:cursor-spacing=\"cursorSpacing\"\n\t\t\t\t@input=\"onInput\"\n\t\t\t\t@blur=\"_Blur\"\n\t\t\t\t@focus=\"_Focus\"\n\t\t\t\t@confirm=\"onConfirm\"\n        @keyboardheightchange=\"onkeyboardheightchange\"\n\t\t\t></textarea>\n\t\t\t<input\n\t\t\t\tv-else\n\t\t\t\t:type=\"type === 'password' ? 'text' : type\"\n\t\t\t\tclass=\"uni-easyinput__content-input\"\n\t\t\t\t:style=\"inputStyle\"\n\t\t\t\t:name=\"name\"\n\t\t\t\t:value=\"val\"\n\t\t\t\t:password=\"!showPassword && type === 'password'\"\n\t\t\t\t:placeholder=\"placeholder\"\n\t\t\t\t:placeholderStyle=\"placeholderStyle\"\n\t\t\t\tplaceholder-class=\"uni-easyinput__placeholder-class\"\n\t\t\t\t:disabled=\"disabled\"\n\t\t\t\t:maxlength=\"inputMaxlength\"\n\t\t\t\t:focus=\"focused\"\n\t\t\t\t:confirmType=\"confirmType\"\n\t\t\t\t:cursor-spacing=\"cursorSpacing\"\n\t\t\t\t@focus=\"_Focus\"\n\t\t\t\t@blur=\"_Blur\"\n\t\t\t\t@input=\"onInput\"\n\t\t\t\t@confirm=\"onConfirm\"\n        @keyboardheightchange=\"onkeyboardheightchange\"\n\t\t\t/>\n\t\t\t<template v-if=\"type === 'password' && passwordIcon\">\n\t\t\t\t<!-- 开启密码时显示小眼睛 -->\n\t\t\t\t<uni-icons\n\t\t\t\t\tv-if=\"isVal\"\n\t\t\t\t\tclass=\"content-clear-icon\"\n\t\t\t\t\t:class=\"{ 'is-textarea-icon': type === 'textarea' }\"\n\t\t\t\t\t:type=\"showPassword ? 'eye-slash-filled' : 'eye-filled'\"\n\t\t\t\t\t:size=\"22\"\n\t\t\t\t\t:color=\"focusShow ? primaryColor : '#c0c4cc'\"\n\t\t\t\t\t@click=\"onEyes\"\n\t\t\t\t></uni-icons>\n\t\t\t</template>\n\t\t\t<template v-else-if=\"suffixIcon\">\n\t\t\t\t<uni-icons v-if=\"suffixIcon\" class=\"content-clear-icon\" :type=\"suffixIcon\" color=\"#c0c4cc\" @click=\"onClickIcon('suffix')\" size=\"22\"></uni-icons>\n\t\t\t</template>\n\t\t\t<template v-else>\n\t\t\t\t<uni-icons\n\t\t\t\t\tv-if=\"clearable && isVal && !disabled && type !== 'textarea'\"\n\t\t\t\t\tclass=\"content-clear-icon\"\n\t\t\t\t\t:class=\"{ 'is-textarea-icon': type === 'textarea' }\"\n\t\t\t\t\ttype=\"clear\"\n\t\t\t\t\t:size=\"clearSize\"\n\t\t\t\t\t:color=\"msg ? '#dd524d' : focusShow ? primaryColor : '#c0c4cc'\"\n\t\t\t\t\t@click=\"onClear\"\n\t\t\t\t></uni-icons>\n\t\t\t</template>\n\t\t\t<slot name=\"right\"></slot>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n/**\n * Easyinput 输入框\n * @description 此组件可以实现表单的输入与校验，包括 \"text\" 和 \"textarea\" 类型。\n * @tutorial https://ext.dcloud.net.cn/plugin?id=3455\n * @property {String}\tvalue\t输入内容\n * @property {String }\ttype\t输入框的类型（默认text） password/text/textarea/..\n * \t@value text\t\t\t文本输入键盘\n * \t@value textarea\t多行文本输入键盘\n * \t@value password\t密码输入键盘\n * \t@value number\t\t数字输入键盘，注意iOS上app-vue弹出的数字键盘并非9宫格方式\n * \t@value idcard\t\t身份证输入键盘，信、支付宝、百度、QQ小程序\n * \t@value digit\t\t带小数点的数字键盘\t，App的nvue页面、微信、支付宝、百度、头条、QQ小程序支持\n * @property {Boolean}\tclearable\t是否显示右侧清空内容的图标控件，点击可清空输入框内容（默认true）\n * @property {Boolean}\tautoHeight\t是否自动增高输入区域，type为textarea时有效（默认true）\n * @property {String }\tplaceholder\t输入框的提示文字\n * @property {String }\tplaceholderStyle\tplaceholder的样式(内联样式，字符串)，如\"color: #ddd\"\n * @property {Boolean}\tfocus\t是否自动获得焦点（默认false）\n * @property {Boolean}\tdisabled\t是否禁用（默认false）\n * @property {Number }\tmaxlength\t最大输入长度，设置为 -1 的时候不限制最大长度（默认140）\n * @property {String }\tconfirmType\t设置键盘右下角按钮的文字，仅在type=\"text\"时生效（默认done）\n * @property {Number }\tclearSize\t清除图标的大小，单位px（默认15）\n * @property {String}\tprefixIcon\t输入框头部图标\n * @property {String}\tsuffixIcon\t输入框尾部图标\n * @property {String}\tprimaryColor\t设置主题色（默认#2979ff）\n * @property {Boolean}\ttrim\t是否自动去除两端的空格\n * @property {Boolean}\tcursorSpacing\t指定光标与键盘的距离，单位 px\n * @value both\t去除两端空格\n * @value left\t去除左侧空格\n * @value right\t去除右侧空格\n * @value start\t去除左侧空格\n * @value end\t\t去除右侧空格\n * @value all\t\t去除全部空格\n * @value none\t不去除空格\n * @property {Boolean}\tinputBorder\t是否显示input输入框的边框（默认true）\n * @property {Boolean}\tpasswordIcon\ttype=password时是否显示小眼睛图标\n * @property {Object}\tstyles\t自定义颜色\n * @event {Function}\tinput\t输入框内容发生变化时触发\n * @event {Function}\tfocus\t输入框获得焦点时触发\n * @event {Function}\tblur\t输入框失去焦点时触发\n * @event {Function}\tconfirm\t点击完成按钮时触发\n * @event {Function}\ticonClick\t点击图标时触发\n * @example <uni-easyinput v-model=\"mobile\"></uni-easyinput>\n */\nfunction obj2strClass(obj) {\n\tlet classess = '';\n\tfor (let key in obj) {\n\t\tconst val = obj[key];\n\t\tif (val) {\n\t\t\tclassess += `${key} `;\n\t\t}\n\t}\n\treturn classess;\n}\n\nfunction obj2strStyle(obj) {\n\tlet style = '';\n\tfor (let key in obj) {\n\t\tconst val = obj[key];\n\t\tstyle += `${key}:${val};`;\n\t}\n\treturn style;\n}\nexport default {\n\tname: 'uni-easyinput',\n\temits: ['click', 'iconClick', 'update:modelValue', 'input', 'focus', 'blur', 'confirm', 'clear', 'eyes', 'change', 'keyboardheightchange'],\n\tmodel: {\n\t\tprop: 'modelValue',\n\t\tevent: 'update:modelValue'\n\t},\n\toptions: {\n\t\tvirtualHost: true\n\t},\n\tinject: {\n\t\tform: {\n\t\t\tfrom: 'uniForm',\n\t\t\tdefault: null\n\t\t},\n\t\tformItem: {\n\t\t\tfrom: 'uniFormItem',\n\t\t\tdefault: null\n\t\t}\n\t},\n\tprops: {\n\t\tname: String,\n\t\tvalue: [Number, String],\n\t\tmodelValue: [Number, String],\n\t\ttype: {\n\t\t\ttype: String,\n\t\t\tdefault: 'text'\n\t\t},\n\t\tclearable: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\tautoHeight: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tplaceholder: {\n\t\t\ttype: String,\n\t\t\tdefault: ' '\n\t\t},\n\t\tplaceholderStyle: String,\n\t\tfocus: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tdisabled: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: false\n\t\t},\n\t\tmaxlength: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 140\n\t\t},\n\t\tconfirmType: {\n\t\t\ttype: String,\n\t\t\tdefault: 'done'\n\t\t},\n\t\tclearSize: {\n\t\t\ttype: [Number, String],\n\t\t\tdefault: 24\n\t\t},\n\t\tinputBorder: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\tprefixIcon: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\tsuffixIcon: {\n\t\t\ttype: String,\n\t\t\tdefault: ''\n\t\t},\n\t\ttrim: {\n\t\t\ttype: [Boolean, String],\n\t\t\tdefault: false\n\t\t},\n\t\tcursorSpacing: {\n\t\t\ttype: Number,\n\t\t\tdefault: 0\n\t\t},\n\t\tpasswordIcon: {\n\t\t\ttype: Boolean,\n\t\t\tdefault: true\n\t\t},\n\t\tprimaryColor: {\n\t\t\ttype: String,\n\t\t\tdefault: '#2979ff'\n\t\t},\n\t\tstyles: {\n\t\t\ttype: Object,\n\t\t\tdefault() {\n\t\t\t\treturn {\n\t\t\t\t\tcolor: '#333',\n\t\t\t\t\tbackgroundColor: '#fff',\n\t\t\t\t\tdisableColor: '#F7F6F6',\n\t\t\t\t\tborderColor: '#e5e5e5'\n\t\t\t\t};\n\t\t\t}\n\t\t},\n\t\terrorMessage: {\n\t\t\ttype: [String, Boolean],\n\t\t\tdefault: ''\n\t\t}\n\t},\n\tdata() {\n\t\treturn {\n\t\t\tfocused: false,\n\t\t\tval: '',\n\t\t\tshowMsg: '',\n\t\t\tborder: false,\n\t\t\tisFirstBorder: false,\n\t\t\tshowClearIcon: false,\n\t\t\tshowPassword: false,\n\t\t\tfocusShow: false,\n\t\t\tlocalMsg: '',\n\t\t\tisEnter: false // 用于判断当前是否是使用回车操作\n\t\t};\n\t},\n\tcomputed: {\n\t\t// 输入框内是否有值\n\t\tisVal() {\n\t\t\tconst val = this.val;\n\t\t\t// fixed by mehaotian 处理值为0的情况，字符串0不在处理范围\n\t\t\tif (val || val === 0) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t\treturn false;\n\t\t},\n\n\t\tmsg() {\n\t\t\t// console.log('computed', this.form, this.formItem);\n\t\t\t// if (this.form) {\n\t\t\t// \treturn this.errorMessage || this.formItem.errMsg;\n\t\t\t// }\n\t\t\t// TODO 处理头条 formItem 中 errMsg 不更新的问题\n\t\t\treturn this.localMsg || this.errorMessage;\n\t\t},\n\t\t// 因为uniapp的input组件的maxlength组件必须要数值，这里转为数值，用户可以传入字符串数值\n\t\tinputMaxlength() {\n\t\t\treturn Number(this.maxlength);\n\t\t},\n\n\t\t// 处理外层样式的style\n\t\tboxStyle() {\n\t\t\treturn `color:${this.inputBorder && this.msg ? '#e43d33' : this.styles.color};`;\n\t\t},\n\t\t// input 内容的类和样式处理\n\t\tinputContentClass() {\n\t\t\treturn obj2strClass({\n\t\t\t\t'is-input-border': this.inputBorder,\n\t\t\t\t'is-input-error-border': this.inputBorder && this.msg,\n\t\t\t\t'is-textarea': this.type === 'textarea',\n\t\t\t\t'is-disabled': this.disabled,\n\t\t\t\t'is-focused': this.focusShow\n\t\t\t});\n\t\t},\n\t\tinputContentStyle() {\n\t\t\tconst focusColor = this.focusShow ? this.primaryColor : this.styles.borderColor;\n\t\t\tconst borderColor = this.inputBorder && this.msg ? '#dd524d' : focusColor;\n\t\t\treturn obj2strStyle({\n\t\t\t\t'border-color': borderColor || '#e5e5e5',\n\t\t\t\t'background-color': this.disabled ? this.styles.disableColor : this.styles.backgroundColor\n\t\t\t});\n\t\t},\n\t\t// input右侧样式\n\t\tinputStyle() {\n\t\t\tconst paddingRight = this.type === 'password' || this.clearable || this.prefixIcon ? '' : '10px';\n\t\t\treturn obj2strStyle({\n\t\t\t\t'padding-right': paddingRight,\n\t\t\t\t'padding-left': this.prefixIcon ? '' : '10px'\n\t\t\t});\n\t\t}\n\t},\n\twatch: {\n\t\tvalue(newVal) {\n\t\t\tthis.val = newVal;\n\t\t},\n\t\tmodelValue(newVal) {\n\t\t\tthis.val = newVal;\n\t\t},\n\t\tfocus(newVal) {\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.focused = this.focus;\n\t\t\t\tthis.focusShow = this.focus;\n\t\t\t});\n\t\t}\n\t},\n\tcreated() {\n\t\tthis.init();\n\t\t// TODO 处理头条vue3 computed 不监听 inject 更改的问题（formItem.errMsg）\n\t\tif (this.form && this.formItem) {\n\t\t\tthis.$watch('formItem.errMsg', newVal => {\n\t\t\t\tthis.localMsg = newVal;\n\t\t\t});\n\t\t}\n\t},\n\tmounted() {\n\t\tthis.$nextTick(() => {\n\t\t\tthis.focused = this.focus;\n\t\t\tthis.focusShow = this.focus;\n\t\t});\n\t},\n\tmethods: {\n\t\t/**\n\t\t * 初始化变量值\n\t\t */\n\t\tinit() {\n\t\t\tif (this.value || this.value === 0) {\n\t\t\t\tthis.val = this.value;\n\t\t\t} else if (this.modelValue || this.modelValue === 0 || this.modelValue === '') {\n\t\t\t\tthis.val = this.modelValue;\n\t\t\t} else {\n\t\t\t\tthis.val = null;\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * 点击图标时触发\n\t\t * @param {Object} type\n\t\t */\n\t\tonClickIcon(type) {\n\t\t\tthis.$emit('iconClick', type);\n\t\t},\n\n\t\t/**\n\t\t * 显示隐藏内容，密码框时生效\n\t\t */\n\t\tonEyes() {\n\t\t\tthis.showPassword = !this.showPassword;\n\t\t\tthis.$emit('eyes', this.showPassword);\n\t\t},\n\n\t\t/**\n\t\t * 输入时触发\n\t\t * @param {Object} event\n\t\t */\n\t\tonInput(event) {\n\t\t\tlet value = event.detail.value;\n\t\t\t// 判断是否去除空格\n\t\t\tif (this.trim) {\n\t\t\t\tif (typeof this.trim === 'boolean' && this.trim) {\n\t\t\t\t\tvalue = this.trimStr(value);\n\t\t\t\t}\n\t\t\t\tif (typeof this.trim === 'string') {\n\t\t\t\t\tvalue = this.trimStr(value, this.trim);\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (this.errMsg) this.errMsg = '';\n\t\t\tthis.val = value;\n\t\t\t// TODO 兼容 vue2\n\t\t\tthis.$emit('input', value);\n\t\t\t// TODO　兼容　vue3\n\t\t\tthis.$emit('update:modelValue', value);\n\t\t},\n\n\t\t/**\n\t\t * 外部调用方法\n\t\t * 获取焦点时触发\n\t\t * @param {Object} event\n\t\t */\n\t\tonFocus() {\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.focused = true;\n\t\t\t});\n\t\t\tthis.$emit('focus', null);\n\t\t},\n\n\t\t_Focus(event) {\n\t\t\tthis.focusShow = true;\n\t\t\tthis.$emit('focus', event);\n\t\t},\n\n\t\t/**\n\t\t * 外部调用方法\n\t\t * 失去焦点时触发\n\t\t * @param {Object} event\n\t\t */\n\t\tonBlur() {\n\t\t\tthis.focused = false;\n\t\t\tthis.$emit('focus', null);\n\t\t},\n\t\t_Blur(event) {\n\t\t\tlet value = event.detail.value;\n\t\t\tthis.focusShow = false;\n\t\t\tthis.$emit('blur', event);\n\t\t\t// 根据类型返回值，在event中获取的值理论上讲都是string\n\t\t\tif (this.isEnter === false) {\n\t\t\t\tthis.$emit('change', this.val);\n\t\t\t}\n\t\t\t// 失去焦点时参与表单校验\n\t\t\tif (this.form && this.formItem) {\n\t\t\t\tconst { validateTrigger } = this.form;\n\t\t\t\tif (validateTrigger === 'blur') {\n\t\t\t\t\tthis.formItem.onFieldChange();\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\t/**\n\t\t * 按下键盘的发送键\n\t\t * @param {Object} e\n\t\t */\n\t\tonConfirm(e) {\n\t\t\tthis.$emit('confirm', this.val);\n\t\t\tthis.isEnter = true;\n\t\t\tthis.$emit('change', this.val);\n\t\t\tthis.$nextTick(() => {\n\t\t\t\tthis.isEnter = false;\n\t\t\t});\n\t\t},\n\n\t\t/**\n\t\t * 清理内容\n\t\t * @param {Object} event\n\t\t */\n\t\tonClear(event) {\n\t\t\tthis.val = '';\n\t\t\t// TODO 兼容 vue2\n\t\t\tthis.$emit('input', '');\n\t\t\t// TODO 兼容 vue2\n\t\t\t// TODO　兼容　vue3\n\t\t\tthis.$emit('update:modelValue', '');\n\t\t\t// 点击叉号触发\n\t\t\tthis.$emit('clear');\n\t\t},\n\n    /**\n     * 键盘高度发生变化的时候触发此事件\n     * 兼容性：微信小程序2.7.0+、App 3.1.0+\n     * @param {Object} event\n     */\n    onkeyboardheightchange(event) {\n      this.$emit(\"keyboardheightchange\",event);\n    },\n\n\t\t/**\n\t\t * 去除空格\n\t\t */\n\t\ttrimStr(str, pos = 'both') {\n\t\t\tif (pos === 'both') {\n\t\t\t\treturn str.trim();\n\t\t\t} else if (pos === 'left') {\n\t\t\t\treturn str.trimLeft();\n\t\t\t} else if (pos === 'right') {\n\t\t\t\treturn str.trimRight();\n\t\t\t} else if (pos === 'start') {\n\t\t\t\treturn str.trimStart();\n\t\t\t} else if (pos === 'end') {\n\t\t\t\treturn str.trimEnd();\n\t\t\t} else if (pos === 'all') {\n\t\t\t\treturn str.replace(/\\s+/g, '');\n\t\t\t} else if (pos === 'none') {\n\t\t\t\treturn str;\n\t\t\t}\n\t\t\treturn str;\n\t\t}\n\t}\n};\n</script>\n\n<style lang=\"scss\">\n$uni-error: #e43d33;\n$uni-border-1: #dcdfe6 !default;\n\n.uni-easyinput {\n\t/* #ifndef APP-NVUE */\n\twidth: 100%;\n\t/* #endif */\n\tflex: 1;\n\tposition: relative;\n\ttext-align: left;\n\tcolor: #333;\n\tfont-size: 14px;\n}\n\n.uni-easyinput__content {\n\tflex: 1;\n\t/* #ifndef APP-NVUE */\n\twidth: 100%;\n\tdisplay: flex;\n\tbox-sizing: border-box;\n\t// min-height: 36px;\n\t/* #endif */\n\tflex-direction: row;\n\talign-items: center;\n\t// 处理border动画刚开始显示黑色的问题\n\tborder-color: #fff;\n\ttransition-property: border-color;\n\ttransition-duration: 0.3s;\n}\n\n.uni-easyinput__content-input {\n\t/* #ifndef APP-NVUE */\n\twidth: auto;\n\t/* #endif */\n\tposition: relative;\n\toverflow: hidden;\n\tflex: 1;\n\tline-height: 1;\n\tfont-size: 14px;\n\theight: 35px;\n\t// min-height: 36px;\n}\n\n.uni-easyinput__placeholder-class {\n\tcolor: #999;\n\tfont-size: 12px;\n\t// font-weight: 200;\n}\n\n.is-textarea {\n\talign-items: flex-start;\n}\n\n.is-textarea-icon {\n\tmargin-top: 5px;\n}\n\n.uni-easyinput__content-textarea {\n\tposition: relative;\n\toverflow: hidden;\n\tflex: 1;\n\tline-height: 1.5;\n\tfont-size: 14px;\n\tmargin: 6px;\n\tmargin-left: 0;\n\theight: 80px;\n\tmin-height: 80px;\n\t/* #ifndef APP-NVUE */\n\tmin-height: 80px;\n\twidth: auto;\n\t/* #endif */\n}\n\n.input-padding {\n\tpadding-left: 10px;\n}\n\n.content-clear-icon {\n\tpadding: 0 5px;\n}\n\n.label-icon {\n\tmargin-right: 5px;\n\tmargin-top: -1px;\n}\n\n// 显示边框\n.is-input-border {\n\t/* #ifndef APP-NVUE */\n\tdisplay: flex;\n\tbox-sizing: border-box;\n\t/* #endif */\n\tflex-direction: row;\n\talign-items: center;\n\tborder: 1px solid $uni-border-1;\n\tborder-radius: 4px;\n\t/* #ifdef MP-ALIPAY */\n\toverflow: hidden;\n\t/* #endif */\n}\n\n.uni-error-message {\n\tposition: absolute;\n\tbottom: -17px;\n\tleft: 0;\n\tline-height: 12px;\n\tcolor: $uni-error;\n\tfont-size: 12px;\n\ttext-align: left;\n}\n\n.uni-error-msg--boeder {\n\tposition: relative;\n\tbottom: 0;\n\tline-height: 22px;\n}\n\n.is-input-error-border {\n\tborder-color: $uni-error;\n\n\t.uni-easyinput__placeholder-class {\n\t\tcolor: mix(#fff, $uni-error, 50%);\n\t}\n}\n\n.uni-easyinput--border {\n\tmargin-bottom: 0;\n\tpadding: 10px 15px;\n\t// padding-bottom: 0;\n\tborder-top: 1px #eee solid;\n}\n\n.uni-easyinput-error {\n\tpadding-bottom: 0;\n}\n\n.is-first-border {\n\t/* #ifndef APP-NVUE */\n\tborder: none;\n\t/* #endif */\n\t/* #ifdef APP-NVUE */\n\tborder-width: 0;\n\t/* #endif */\n}\n\n.is-disabled {\n\tbackground-color: #f7f6f6;\n\tcolor: #d5d5d5;\n\n\t.uni-easyinput__placeholder-class {\n\t\tcolor: #d5d5d5;\n\t\tfont-size: 12px;\n\t}\n}\n</style>\n", "import mod from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-easyinput.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\前端HTML\\\\工具\\\\HBuilderX.3.6.5.20221121\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-easyinput.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753343925204\n      var cssReload = require(\"E:/前端HTML/工具/HBuilderX.3.6.5.20221121/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}