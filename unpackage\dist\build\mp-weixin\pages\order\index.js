(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/order/index"],{"03d1":function(n,t,e){"use strict";e.r(t);var a=e("b852"),r=e("796a");for(var i in r)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return r[n]}))}(i);e("753a"),e("658b");var u=e("828b"),c=Object(u["a"])(r["default"],a["b"],a["c"],!1,null,"4f88a28e",null,!1,a["a"],void 0);t["default"]=c.exports},b852:function(n,t,e){"use strict";e.d(t,"b",(function(){return r})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){return a}));var a={uniNavBar:function(){return e.e("components/uni-nav-bar/uni-nav-bar").then(e.bind(null,"7858"))}},r=function(){var n=this.$createElement,t=(this._self._c,this.orderDishPrice.toFixed(2));this.$mp.data=Object.assign({},{$root:{g0:t}})},i=[]},fc95:function(n,t,e){"use strict";(function(n,t){var a=e("47a9");e("b759");a(e("3240"));var r=a(e("03d1"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(r.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])}},[["fc95","common/runtime","common/vendor"]]]);