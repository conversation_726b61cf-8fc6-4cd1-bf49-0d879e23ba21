(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/remark/index"],{"57c6":function(t,e,r){"use strict";r.r(e);var n=r("68c0"),a=r("879d");for(var i in a)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(i);r("151e"),r("1ac7");var u=r("828b"),c=Object(u["a"])(a["default"],n["b"],n["c"],!1,null,"745a5bfa",null,!1,n["a"],void 0);e["default"]=c.exports},"68c0":function(t,e,r){"use strict";r.d(e,"b",(function(){return a})),r.d(e,"c",(function(){return i})),r.d(e,"a",(function(){return n}));var n={uniNavBar:function(){return r.e("components/uni-nav-bar/uni-nav-bar").then(r.bind(null,"7858"))}},a=function(){var t=this.$createElement;this._self._c},i=[]},"753b":function(t,e,r){"use strict";(function(t){var n=r("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(r("7ca3")),i=r("8f59");function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach((function(e){(0,a.default)(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}var o={data:function(){return{remark:"",numVal:0}},computed:{getVal:function(){var t=this.validateTextLength(this.remark);t<=50?this.numVal=Math.floor(t):this.remark=this.remark.substring(0,50)}},onLoad:function(){console.log(this.remarkData()),""===this.getRemark?this.remark=this.remark:(this.remark=this.remarkData(),this.numVal=this.remark.length)},methods:c(c(c({},(0,i.mapMutations)(["setRemark"])),(0,i.mapState)(["remarkData"])),{},{goBack:function(){t.redirectTo({url:"/pages/order/index"})},handleSaveRemark:function(){t.redirectTo({url:"/pages/order/index"}),this.setRemark(this.remark)},validateTextLength:function(t){var e,r=t.match(/([\u4e00-\u9fa5]|[\u3000-\u303F]|[\uFF00-\uFF60])/g);return r?(e=r.length+.5*(t.length-r.length),e):.5*t.length}})};e.default=o}).call(this,r("df3c")["default"])},"879d":function(t,e,r){"use strict";r.r(e);var n=r("753b"),a=r.n(n);for(var i in n)["default"].indexOf(i)<0&&function(t){r.d(e,t,(function(){return n[t]}))}(i);e["default"]=a.a},d944:function(t,e,r){"use strict";(function(t,e){var n=r("47a9");r("b759");n(r("3240"));var a=n(r("57c6"));t.__webpack_require_UNI_MP_PLUGIN__=r,e(a.default)}).call(this,r("3223")["default"],r("df3c")["createPage"])}},[["d944","common/runtime","common/vendor"]]]);