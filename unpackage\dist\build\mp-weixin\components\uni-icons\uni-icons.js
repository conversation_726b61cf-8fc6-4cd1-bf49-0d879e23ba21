(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/uni-icons/uni-icons"],{"3db4":function(n,t,e){"use strict";e.r(t);var u=e("5424"),i=e("905f");for(var c in i)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(c);e("84e2");var r=e("828b"),o=Object(r["a"])(i["default"],u["b"],u["c"],!1,null,"1217d9c2",null,!1,u["a"],void 0);t["default"]=o.exports},"4ee6":function(n,t,e){"use strict";var u=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=u(e("9583")),c={name:"UniIcons",props:{type:{type:String,default:""},color:{type:String,default:"#333333"},size:{type:[Number,String],default:16}},data:function(){return{icons:i.default}},methods:{_onClick:function(){this.$emit("click")}}};t.default=c},5424:function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){}));var u=function(){var n=this.$createElement;this._self._c},i=[]},"84e2":function(n,t,e){"use strict";var u=e("fd8b"),i=e.n(u);i.a},"905f":function(n,t,e){"use strict";e.r(t);var u=e("4ee6"),i=e.n(u);for(var c in u)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(c);t["default"]=i.a},fd8b:function(n,t,e){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/uni-icons/uni-icons-create-component',
    {
        'components/uni-icons/uni-icons-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("3db4"))
        })
    },
    [['components/uni-icons/uni-icons-create-component']]
]);
