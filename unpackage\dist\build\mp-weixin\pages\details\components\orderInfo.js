(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/details/components/orderInfo"],{6411:function(t,n,e){"use strict";e.r(n);var u=e("c2a0"),r=e("cd2f");for(var a in r)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return r[t]}))}(a);e("0e3a");var c=e("828b"),f=Object(c["a"])(r["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);n["default"]=f.exports},c2a0:function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return r})),e.d(n,"a",(function(){}));var u=function(){var t=this.$createElement;this._self._c},r=[]},cb99:function(t,n,e){"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u={props:{orderDetailsData:{type:Object,default:function(){return{}}}}};n.default=u},cd2f:function(t,n,e){"use strict";e.r(n);var u=e("cb99"),r=e.n(u);for(var a in u)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(a);n["default"]=r.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages/details/components/orderInfo-create-component',
    {
        'pages/details/components/orderInfo-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("6411"))
        })
    },
    [['pages/details/components/orderInfo-create-component']]
]);
