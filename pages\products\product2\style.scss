.product-container {
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
}

.order-button-container {
    width: 100%;
    height: 33.33vh;
    /* 占据屏幕高度的三分之一 */
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 60rpx;

    .order-button {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #ff6b35, #ff8c42);
        color: #fff;
        font-size: 72rpx;
        font-weight: bold;
        border-radius: 40rpx;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 20rpx 60rpx rgba(255, 107, 53, 0.4);
        transition: all 0.3s ease;
        letter-spacing: 8rpx;

        &:active {
            transform: translateY(6rpx) scale(0.98);
            box-shadow: 0 15rpx 45rpx rgba(255, 107, 53, 0.6);
        }

        &:hover {
            transform: translateY(-3rpx);
            box-shadow: 0 25rpx 70rpx rgba(255, 107, 53, 0.5);
        }
    }
}