# Mid 参数优化功能说明

## 功能概述

优化了 mid 参数的管理机制，确保当界面有 mid 时能持续保存，当 mid 更改时也能正确更新保存的值，并根据不同的 mid 展示不同的店铺信息。

## 主要优化点

### 1. 统一的 Mid 参数管理

- **initMidParameter(options)**: 初始化 mid 参数，优先使用 URL 参数，其次使用本地存储
- **ensureMidParameter()**: 确保 mid 参数在页面显示时正确同步
- **updateMid(newMid)**: 统一的 mid 更新方法，处理参数变化和数据同步
- **setMidParameter(mid)**: 供外部调用的手动设置方法

### 2. 持久化存储优化

- 自动保存 mid 参数到本地存储
- 支持 mid 历史记录功能（最多保存10个）
- 智能检测参数变化，避免重复保存
- 支持清除功能

### 3. 店铺信息动态配置

- 根据 mid 参数动态显示店铺名称、地址、电话
- 支持默认配置和多个自定义配置
- 计算属性自动更新界面显示

### 4. 事件监听机制

- 监听 mid 参数变化
- 自动触发店铺信息更新
- 支持自定义事件通知

## 配置文件结构

### shopConfig.js
```javascript
export const shopConfigs = {
  default: {
    shopName: "测试店铺",
    shopAddress: "商家店铺获取中..",
    phone: "************"
  },
  "1": {
    shopName: "瑞吉外卖(总店)",
    shopAddress: "北京市海淀区中关村大街1号",
    phone: "************"
  },
  "2": {
    shopName: "瑞吉外卖(分店)",
    shopAddress: "北京市朝阳区建国门外大街2号",
    phone: "************"
  }
}
```

## 使用方法

### 1. URL 参数方式
```
/pages/index/index?mid=1
/pages/index/index?mid=2
```

### 2. 程序调用方式
```javascript
// 设置 mid 参数
this.setMidParameter('2')

// 获取历史记录
const history = this.getMidHistory()
```

### 3. 测试页面
访问 `/pages/test-shop/index` 可以方便地测试不同 mid 的效果

## 生命周期处理

### onLoad
1. 检查 URL 参数中的 mid
2. 如果没有，从本地存储获取
3. 调用 initMidParameter 初始化
4. 更新店铺信息

### onShow
1. 确保 mid 参数正确同步
2. 检查本地存储一致性
3. 更新店铺信息显示

## 数据流程

1. **参数获取**: URL参数 → 本地存储 → 默认值
2. **参数保存**: 新参数 → 本地存储 → 历史记录
3. **信息更新**: mid变化 → 获取配置 → 更新Vuex → 界面刷新

## 调试功能

- 控制台日志记录所有 mid 操作
- 测试页面可视化当前配置
- 历史记录查看功能
- 参数变化实时反馈

## 兼容性

- 向后兼容原有功能
- 支持无 mid 参数的默认模式
- 自动处理异常情况
- 优雅降级机制

## 注意事项

1. mid 参数会持久化保存，页面刷新后仍然有效
2. 手动清除需要调用 `midManager.clearMid()`
3. 历史记录最多保存10个，超出会自动删除最旧的
4. 配置文件支持动态扩展，添加新的 mid 配置即可
