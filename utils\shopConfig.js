/**
 * 店铺配置管理
 * 根据不同的 mid 参数展示不同的店铺信息
 */

export const shopConfigs = {
  // 默认店铺配置
  default: {
    shopName: "测试店铺",
    shopAddress: "商家店铺获取中..",
    phone: "************",
    logo: "../../static/logo_ruiji.png"
  },
  
  // mid 为 1 的店铺配置
  "1": {
    shopName: "瑞吉外卖(总店)",
    shopAddress: "北京市海淀区中关村大街1号",
    phone: "************",
    logo: "../../static/logo_ruiji.png"
  },
  
  // mid 为 2 的店铺配置
  "2": {
    shopName: "瑞吉外卖(分店)",
    shopAddress: "北京市朝阳区建国门外大街2号",
    phone: "************",
    logo: "../../static/logo_ruiji.png"
  },
  
  // mid 为 3 的店铺配置
  "3": {
    shopName: "瑞吉外卖(三里屯店)",
    shopAddress: "北京市朝阳区三里屯路3号",
    phone: "************",
    logo: "../../static/logo_ruiji.png"
  }
}

/**
 * 根据 mid 获取店铺配置
 * @param {string|number} mid - mid 参数
 * @returns {object} 店铺配置对象
 */
export function getShopConfigByMid(mid) {
  if (!mid) {
    return shopConfigs.default
  }
  
  const config = shopConfigs[mid.toString()]
  return config || shopConfigs.default
}

/**
 * 获取所有可用的 mid 列表
 * @returns {array} mid 列表
 */
export function getAvailableMids() {
  return Object.keys(shopConfigs).filter(key => key !== 'default')
}

/**
 * 检查 mid 是否有效
 * @param {string|number} mid - mid 参数
 * @returns {boolean} 是否有效
 */
export function isValidMid(mid) {
  if (!mid) return false
  return shopConfigs.hasOwnProperty(mid.toString())
}
