/**
 * 店铺配置管理
 * 根据不同的 mid 参数展示不同的店铺信息
 */

export const shopConfigs = {
  // 默认店铺配置
  default: {
    shopName: "测试店铺1",
    shopAddress: "测试地址1",
    phone: "17680710830",
    logo: "../../static/logo_ruiji.png"
  },

  // mid 为 1 的店铺配置
  "1": {
    shopName: "测试店铺2",
    shopAddress: "测试地址2",
    phone: "17680710830",
    logo: "../../static/logo_ruiji.png"
  },

  // mid 为 2 的店铺配置
  "2": {
    shopName: "测试店铺2",
    shopAddress: "测试地址3",
    phone: "17680710830",
    logo: "../../static/logo_ruiji.png"
  },

  // mid 为 3 的店铺配置
  "3": {
    shopName: "测试店铺3",
    shopAddress: "测试地址3",
    phone: "************",
    logo: "../../static/logo_ruiji.png"
  },
  // mid 为 4 的店铺配置

  "4": {
    shopName: "测试店铺4",
    shopAddress: "测试地址4",
    phone: "************",
    logo: "../../static/logo_ruiji.png"
  },

  "5": {
    shopName: "测试店铺5",
    shopAddress: "测试地址5",
    phone: "************",
    logo: "../../static/logo_ruiji.png"
  },

  "6": {
    shopName: "测试店铺5",
    shopAddress: "测试地址5",
    phone: "************",
    logo: "../../static/logo_ruiji.png"
  },


}

/**
 * 根据 mid 获取店铺配置
 * @param {string|number} mid - mid 参数
 * @returns {object} 店铺配置对象
 */
export function getShopConfigByMid(mid) {
  if (!mid) {
    return shopConfigs.default
  }

  const config = shopConfigs[mid.toString()]
  return config || shopConfigs.default
}

/**
 * 获取所有可用的 mid 列表
 * @returns {array} mid 列表
 */
export function getAvailableMids() {
  return Object.keys(shopConfigs).filter(key => key !== 'default')
}

/**
 * 检查 mid 是否有效
 * @param {string|number} mid - mid 参数
 * @returns {boolean} 是否有效
 */
export function isValidMid(mid) {
  if (!mid) return false
  return shopConfigs.hasOwnProperty(mid.toString())
}
